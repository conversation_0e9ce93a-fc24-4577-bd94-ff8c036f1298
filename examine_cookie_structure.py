#!/usr/bin/env python3
"""
Examine the structure of Chrome cookies database to understand the BLOB issue
"""

import sqlite3
import os

def examine_cookie_structure():
    cookies_db_path = "cookies_temp.db"
    
    if not os.path.exists(cookies_db_path):
        print(f"Error: {cookies_db_path} not found.")
        return
    
    # Try different approaches to handle the BLOB data
    print("🔍 Examining Chrome cookies database structure...")
    print("=" * 60)
    
    # Approach 1: Use bytes mode
    try:
        print("\n1. Trying with default connection...")
        conn = sqlite3.connect(cookies_db_path)
        cursor = conn.cursor()
        
        # Get schema info
        cursor.execute("PRAGMA table_info(cookies)")
        columns = cursor.fetchall()
        print("Columns:")
        for col in columns:
            print(f"  {col[1]}: {col[2]}")
        
        # Try to get just one Instagram cookie without encrypted_value
        cursor.execute("""
            SELECT host_key, name, value, length(encrypted_value) as enc_len
            FROM cookies 
            WHERE host_key LIKE '%instagram.com%' 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            host, name, value, enc_len = result
            print(f"\nSample cookie: {name}")
            print(f"  Host: {host}")
            print(f"  Value: '{value}' (length: {len(value) if value else 0})")
            print(f"  Encrypted value length: {enc_len}")
        
        conn.close()
        print("✅ Basic query successful")
        
    except Exception as e:
        print(f"❌ Basic query failed: {e}")
    
    # Approach 2: Try with binary mode
    try:
        print("\n2. Trying to read encrypted_value with hex...")
        conn = sqlite3.connect(cookies_db_path)
        cursor = conn.cursor()
        
        # Use hex() function to get encrypted value as hex string
        cursor.execute("""
            SELECT host_key, name, hex(encrypted_value) as hex_encrypted
            FROM cookies 
            WHERE host_key LIKE '%instagram.com%' 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            host, name, hex_encrypted = result
            print(f"Sample encrypted cookie: {name}")
            print(f"  Host: {host}")
            print(f"  Encrypted (hex): {hex_encrypted[:100]}...")
            print(f"  Encrypted length: {len(hex_encrypted)//2} bytes")
            
            # Convert hex back to bytes to examine
            encrypted_bytes = bytes.fromhex(hex_encrypted)
            print(f"  First 20 bytes: {encrypted_bytes[:20]}")
            print(f"  Starts with v10: {encrypted_bytes.startswith(b'v10')}")
            print(f"  Starts with v11: {encrypted_bytes.startswith(b'v11')}")
        
        conn.close()
        print("✅ Hex query successful")
        
    except Exception as e:
        print(f"❌ Hex query failed: {e}")
    
    # Approach 3: Check if we can use a different text_factory
    try:
        print("\n3. Trying with bytes text_factory...")
        conn = sqlite3.connect(cookies_db_path)
        conn.text_factory = bytes  # Return everything as bytes
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT host_key, name, value
            FROM cookies 
            WHERE host_key LIKE b'%instagram.com%' 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            host, name, value = result
            print(f"Sample with bytes factory:")
            print(f"  Host: {host}")
            print(f"  Name: {name}")
            print(f"  Value: {value}")
        
        conn.close()
        print("✅ Bytes factory successful")
        
    except Exception as e:
        print(f"❌ Bytes factory failed: {e}")

if __name__ == "__main__":
    examine_cookie_structure()
