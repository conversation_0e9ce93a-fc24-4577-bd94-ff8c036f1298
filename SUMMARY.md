# Project Summary: Chrome Instagram Cookie Extractor

## 🎯 **Project Goal**
Extract and decrypt Instagram cookies from Google Chrome on macOS to access authentication tokens and session data.

## ✅ **What We Accomplished**

### 1. **Problem Identification & Resolution**
- **Initial Issue**: TypeError when accessing Chrome's encrypted cookie values
- **Root Cause**: SQLite trying to decode binary BLOB data as UTF-8 text
- **Solution**: Used hex encoding to safely extract BLOB data

### 2. **Chrome Encryption Algorithm Implementation**
- **Key Derivation**: PBKDF2-HMAC-SHA1 with 1003 iterations and "saltysalt" salt
- **Decryption**: AES-128-CBC with 16 space characters as IV
- **Padding**: Proper PKCS7 padding removal
- **Version Handling**: Support for Chrome's v10 encryption format
- **Hash Prefix**: Automatic removal of SHA256 domain hash for database version ≥ 24

### 3. **Complete Workflow Implementation**
- **Database Copying**: Safe extraction of Chrome's cookies database
- **Keychain Integration**: Automatic retrieval of encryption key from macOS keychain
- **Cookie Filtering**: Specific extraction of Instagram cookies only
- **Data Export**: Clean JSON output format for easy use

## 📁 **Final Project Structure**

### **Main Files (Use These)**
- **`chrome_instagram_cookie_extractor.py`** - ⭐ **MAIN SCRIPT** - Complete unified solution
- **`README.md`** - Comprehensive documentation and usage guide
- **`SUMMARY.md`** - This project summary

### **Development Files (Reference Only)**
- `decrypt_instagram_cookies.py` - Original decryption script
- `explore_cookies_db.py` - Database exploration script  
- `examine_cookie_structure.py` - Debugging script for BLOB issues
- `script.py` - Initial cookie copying script

### **Generated Output Files**
- `instagram_cookies_decrypted.json` - Decrypted Instagram cookies (created when script runs)
- `cookies_temp.db` - Temporary Chrome database copy (auto-deleted)

## 🚀 **How to Use**

### **Quick Start**
```bash
# Install dependencies
pip install cryptography

# Run the complete extraction
python chrome_instagram_cookie_extractor.py

# View results
cat instagram_cookies_decrypted.json
```

### **Command Options**
```bash
# Full extraction (default)
python chrome_instagram_cookie_extractor.py

# Just explore database
python chrome_instagram_cookie_extractor.py --explore-only

# Keep temporary files for debugging
python chrome_instagram_cookie_extractor.py --no-cleanup

# Show help
python chrome_instagram_cookie_extractor.py --help
```

## 🔍 **Key Technical Insights**

### **Why Values Were Empty Initially**
1. Chrome stores actual values in `encrypted_value` BLOB column, not `value` TEXT column
2. Values are encrypted using Chrome's proprietary algorithm with system keychain integration
3. Database version 24+ includes SHA256 hash prefixes that must be removed
4. SQLite was attempting UTF-8 decoding on binary data, causing errors

### **Chrome's Security Model**
- **Encryption Key**: Stored in macOS keychain under "Chrome Safe Storage"
- **Key Derivation**: Uses PBKDF2 to derive AES key from base64-encoded passphrase
- **Domain Verification**: SHA256 hash of domain name included in newer versions
- **Access Control**: Requires keychain permissions to access encryption key

## 📊 **Results Achieved**

### **Successfully Decrypted Instagram Cookies**
- **`sessionid`**: Main authentication token
- **`csrftoken`**: Cross-site request forgery protection token
- **`ds_user_id`**: Instagram user identifier (***********)
- **`ig_did`**: Device identifier UUID
- **`datr`**: Data tracking token
- **`mid`**: Machine identifier
- **`wd`**: Window dimensions (887x938)
- **`ps_l`** & **`ps_n`**: Privacy settings
- **`rur`**: Routing/region information

### **Output Format**
Clean JSON structure with:
- Cookie name and decrypted value
- Domain and path information
- Security flags (secure, httpOnly)
- Timestamp data (creation, expiration, last access)

## 🛡️ **Security Considerations**

### **Ethical Use Only**
- ✅ Personal use on your own Instagram account
- ✅ Educational and research purposes
- ✅ Legitimate automation of your own account

### **Security Best Practices**
- 🔒 Never share your cookies with others
- 🗑️ Delete output files when no longer needed
- 👤 Only use on accounts you own
- ⚠️ Be aware that cookies provide full account access

## 🔧 **Technical Architecture**

### **Class-Based Design**
- `ChromeCookieExtractor` class encapsulates all functionality
- Modular methods for each step of the process
- Proper error handling and cleanup
- Command-line interface with argparse

### **Error Handling**
- Graceful handling of permission errors
- Keychain access error management
- Database corruption detection
- Automatic cleanup on failure

### **Cross-Platform Considerations**
- Currently optimized for macOS
- Uses macOS-specific keychain commands
- Chrome path detection for macOS
- Could be extended for Windows/Linux with platform-specific adaptations

## 🎉 **Project Success Metrics**

✅ **Solved the original TypeError issue**  
✅ **Successfully implemented Chrome's encryption algorithm**  
✅ **Created a user-friendly unified script**  
✅ **Achieved 100% Instagram cookie decryption success rate**  
✅ **Provided comprehensive documentation**  
✅ **Implemented proper security practices**  

## 🔮 **Future Enhancements**

### **Potential Improvements**
- Support for other browsers (Firefox, Safari, Edge)
- Cross-platform compatibility (Windows, Linux)
- GUI interface for non-technical users
- Batch processing for multiple profiles
- Cookie validation and freshness checking
- Integration with Instagram API libraries

### **Advanced Features**
- Cookie refresh automation
- Session management
- Multi-account support
- Encrypted storage of extracted cookies
- Integration with browser automation tools

---

**Final Note**: This project successfully demonstrates advanced browser security research, cryptographic implementation, and practical automation tool development. The unified script provides a complete, professional-grade solution for Instagram cookie extraction on macOS.
