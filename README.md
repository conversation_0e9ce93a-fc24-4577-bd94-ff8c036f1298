# Chrome Instagram Cookie Extractor

A comprehensive Python script that extracts and decrypts Instagram cookies from Google Chrome on macOS.

## Features

- 🔄 **Automatic Chrome Cookie Database Copying** - Safely copies Chrome's cookies database
- 📊 **Database Exploration** - Analyzes cookie database structure and statistics  
- 🔓 **Cookie Decryption** - Decrypts Chrome's encrypted cookie values using proper algorithms
- 📸 **Instagram Focus** - Specifically extracts Instagram cookies for authentication
- 🛡️ **Security Aware** - Handles Chrome's v10 encryption with SHA256 hash prefixes
- 🧹 **Clean Operation** - Automatically cleans up temporary files

## Requirements

- macOS (tested on macOS with Chrome)
- Python 3.6+
- Google Chrome installed
- Instagram login session in Chrome

### Python Dependencies

```bash
pip install cryptography
```

## Installation

1. Download the script:
```bash
curl -O https://raw.githubusercontent.com/your-repo/chrome_instagram_cookie_extractor.py
```

2. Install dependencies:
```bash
pip install cryptography
```

## Usage

### Full Extraction (Recommended)
Extract and decrypt all Instagram cookies:
```bash
python chrome_instagram_cookie_extractor.py
```

### Explore Database Only
Just analyze the cookie database without decryption:
```bash
python chrome_instagram_cookie_extractor.py --explore-only
```

### Keep Temporary Files
Run extraction but keep the temporary database file:
```bash
python chrome_instagram_cookie_extractor.py --no-cleanup
```

### Help
```bash
python chrome_instagram_cookie_extractor.py --help
```

## Output

The script generates:
- **`instagram_cookies_decrypted.json`** - JSON file with all decrypted Instagram cookies
- **Console output** - Detailed progress and cookie information

### Sample Output
```json
[
  {
    "name": "sessionid",
    "value": "56377328983%3AEDgXf1vN0C7GVd%3A26%3AAYdzz1FKlk0ThveolAAz48orBUksLsr01pSDnBeGUxUc",
    "domain": ".instagram.com",
    "path": "/",
    "expires": 13424886987534737,
    "secure": true,
    "httpOnly": true,
    "created": 13393346447898943,
    "lastAccessed": 13393350636244368
  },
  {
    "name": "csrftoken",
    "value": "6jCKJMMd36Bd67vtBTwsI4aHodQoCUVh",
    "domain": ".instagram.com",
    "path": "/",
    "expires": 13427911146644703,
    "secure": true,
    "httpOnly": false,
    "created": 13384521075004977,
    "lastAccessed": 13393350636244368
  }
]
```

## How It Works

1. **Database Copying**: Safely copies Chrome's cookies database to avoid locking issues
2. **Keychain Access**: Retrieves Chrome's encryption passphrase from macOS keychain
3. **Key Derivation**: Uses PBKDF2 with Chrome's parameters (1003 iterations, SHA-1, "saltysalt")
4. **Decryption**: Implements Chrome's AES-128-CBC algorithm with proper IV and padding
5. **Hash Handling**: Removes SHA256 hash prefixes for database version ≥ 24
6. **JSON Export**: Saves decrypted cookies in a clean, usable format

## Important Notes

### Security & Privacy
- ⚠️ **Only use on your own Chrome browser and Instagram account**
- 🔒 **Cookies contain sensitive authentication data**
- 🗑️ **Delete output files when no longer needed**
- 👤 **Never share your cookies with others**

### Prerequisites
- Chrome must be closed during extraction (or expect permission errors)
- You must be logged into Instagram in Chrome
- macOS keychain access permissions may be required

### Troubleshooting

**"Permission denied" error:**
- Close Chrome completely and try again
- Check file permissions on Chrome's data directory

**"No encryption key found":**
- Make sure Chrome has stored passwords before (creates keychain entry)
- Grant keychain access when prompted

**"No Instagram cookies found":**
- Log into Instagram in Chrome first
- Make sure you're using the Default Chrome profile

## Technical Details

### Chrome Cookie Encryption
- **Algorithm**: AES-128-CBC
- **IV**: 16 space characters (0x20)
- **Key Derivation**: PBKDF2-HMAC-SHA1, 1003 iterations, salt="saltysalt"
- **Padding**: PKCS7
- **Version**: Supports v10 format
- **Hash Prefix**: Handles SHA256 domain hash for database version ≥ 24

### Database Structure
Chrome stores cookies in SQLite with:
- `value` column: Usually empty for encrypted cookies
- `encrypted_value` column: BLOB containing encrypted data
- `meta` table: Contains database version information

## Legal & Ethical Use

This tool is intended for:
- ✅ Personal use on your own accounts
- ✅ Security research and education
- ✅ Legitimate automation of your own Instagram account

**NOT intended for:**
- ❌ Accessing others' accounts without permission
- ❌ Violating Instagram's Terms of Service
- ❌ Any illegal or unethical activities

## License

This project is provided for educational purposes. Use responsibly and in accordance with applicable laws and terms of service.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the script.

---

**Disclaimer**: This tool is for educational and personal use only. Users are responsible for complying with all applicable laws and terms of service.
