#!/usr/bin/env python3
"""
Complete Chrome Instagram Cookie Extractor
Combines cookie copying, database exploration, and decryption into one script
Specifically designed for macOS Chrome cookie extraction and decryption
"""

import sqlite3
import os
import sys
import subprocess
import base64
import hashlib
import shutil
from pathlib import Path
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import json
import argparse


class ChromeCookieExtractor:
    def __init__(self):
        self.temp_db_path = "cookies_temp.db"
        self.output_json = "instagram_cookies_decrypted.json"
        self.chrome_cookies_path = os.path.expanduser(
            "~/Library/Application Support/Google/Chrome/Default/Cookies"
        )
    
    def copy_chrome_cookies(self):
        """Copy Chrome cookies database to temporary file"""
        print("🔄 Copying Chrome cookies database...")
        
        if not os.path.exists(self.chrome_cookies_path):
            print(f"❌ Chrome cookies database not found at: {self.chrome_cookies_path}")
            print("Make sure Chrome is installed and you've browsed some websites.")
            return False
        
        try:
            # Remove existing temp file if it exists
            if os.path.exists(self.temp_db_path):
                os.remove(self.temp_db_path)
            
            # Copy the cookies database
            shutil.copy2(self.chrome_cookies_path, self.temp_db_path)
            print(f"✅ Cookies database copied to: {self.temp_db_path}")
            return True
            
        except PermissionError:
            print("❌ Permission denied. Make sure Chrome is closed and try again.")
            return False
        except Exception as e:
            print(f"❌ Error copying cookies database: {e}")
            return False
    
    def get_chrome_encryption_key(self):
        """Get Chrome's encryption key from macOS keychain and derive the AES key"""
        print("🔑 Getting Chrome encryption key from keychain...")
        
        try:
            cmd = [
                'security', 'find-generic-password',
                '-w',  # Output only the password
                '-s', 'Chrome Safe Storage',  # Service name
                '-a', 'Chrome'  # Account name
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            passphrase = result.stdout.strip()
            
            if not passphrase:
                raise Exception("No encryption key found in keychain")
            
            # The passphrase from keychain is base64-encoded, use it directly
            passphrase_bytes = passphrase.encode('utf-8')
            
            # Derive the AES key using PBKDF2 with Chrome's parameters
            salt = b'saltysalt'
            iterations = 1003
            key = hashlib.pbkdf2_hmac('sha1', passphrase_bytes, salt, iterations, 16)
            
            print(f"✅ Encryption key obtained (length: {len(key)} bytes)")
            return key
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Error accessing keychain: {e}")
            print("Make sure you have permission to access Chrome's keychain entry")
            return None
        except Exception as e:
            print(f"❌ Error getting encryption key: {e}")
            return None
    
    def get_database_version(self, cursor):
        """Get the Chrome database version to check if SHA256 hash is included"""
        try:
            cursor.execute("SELECT value FROM meta WHERE key = 'version'")
            result = cursor.fetchone()
            return int(result[0]) if result else 0
        except:
            return 0
    
    def decrypt_chrome_cookie_value(self, encrypted_value, key, host_key, db_version):
        """Decrypt Chrome cookie value using AES-128-CBC with proper Chrome algorithm"""
        try:
            if not encrypted_value or len(encrypted_value) < 3:
                return ""
                
            # Check version tag
            version = encrypted_value[:3]
            
            if version != b'v10':
                return f"[Unsupported version: {version}]"
            
            # Strip version tag
            encrypted_payload = encrypted_value[3:]
            
            # Chrome uses AES-128-CBC with 16 space characters as IV
            iv = b' ' * 16  # 16 space characters (0x20)
            
            # Decrypt using AES-128-CBC
            cipher = Cipher(
                algorithms.AES(key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(encrypted_payload) + decryptor.finalize()
            
            # Remove PKCS7 padding
            padding_length = decrypted[-1]
            if padding_length > 16 or padding_length == 0:
                raise ValueError(f"Invalid padding length: {padding_length}")
            
            # Verify padding
            padding_bytes = decrypted[-padding_length:]
            if not all(byte == padding_length for byte in padding_bytes):
                raise ValueError("Invalid padding bytes")
            
            # Remove padding
            decrypted_data = decrypted[:-padding_length]
            
            # Check if we need to remove SHA256 hash (database version >= 24)
            if db_version >= 24:
                if len(decrypted_data) >= 32:
                    # Verify the hash matches (optional verification)
                    expected_hash = hashlib.sha256(host_key.encode('utf-8')).digest()
                    actual_hash = decrypted_data[:32]
                    if expected_hash != actual_hash:
                        print(f"⚠️  Warning: SHA256 hash mismatch for {host_key}")
                    
                    decrypted_data = decrypted_data[32:]
                else:
                    print(f"⚠️  Warning: Decrypted data too short to contain hash for {host_key}")
            
            # Decode to string
            return decrypted_data.decode('utf-8', errors='replace')
                
        except Exception as e:
            return f"[Decryption failed: {str(e)}]"
    
    def explore_database(self):
        """Explore the cookies database and show general statistics"""
        print("\n📊 Exploring cookies database...")
        
        if not os.path.exists(self.temp_db_path):
            print(f"❌ Database file not found: {self.temp_db_path}")
            return
        
        conn = sqlite3.connect(self.temp_db_path)
        cursor = conn.cursor()
        
        try:
            # Get table schema
            cursor.execute("SELECT sql FROM sqlite_schema WHERE type='table' AND name='cookies'")
            schema = cursor.fetchone()
            if schema:
                print("✅ Cookies table found")
            else:
                print("❌ Cookies table not found")
                return
            
            # Count total cookies
            cursor.execute("SELECT COUNT(*) FROM cookies")
            total_count = cursor.fetchone()[0]
            print(f"📈 Total cookies in database: {total_count}")
            
            # Count Instagram cookies
            cursor.execute("SELECT COUNT(*) FROM cookies WHERE host_key LIKE '%instagram.com%'")
            instagram_count = cursor.fetchone()[0]
            print(f"📸 Instagram cookies found: {instagram_count}")
            
            # Get database version
            db_version = self.get_database_version(cursor)
            print(f"🔢 Chrome database version: {db_version}")
            
            if instagram_count == 0:
                print("⚠️  No Instagram cookies found. Make sure you're logged into Instagram in Chrome.")
            
        except Exception as e:
            print(f"❌ Error exploring database: {e}")
        finally:
            conn.close()
    
    def extract_and_decrypt_instagram_cookies(self):
        """Extract and decrypt Instagram cookies"""
        print("\n🔓 Extracting and decrypting Instagram cookies...")
        
        if not os.path.exists(self.temp_db_path):
            print(f"❌ Database file not found: {self.temp_db_path}")
            return False
        
        # Get encryption key
        encryption_key = self.get_chrome_encryption_key()
        if not encryption_key:
            print("❌ Failed to get encryption key. Cannot decrypt cookies.")
            return False
        
        # Connect to database
        conn = sqlite3.connect(self.temp_db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Get database version
            db_version = self.get_database_version(cursor)
            if db_version >= 24:
                print("📝 Note: Database includes SHA256 hash prefix (will be removed during decryption)")
            
            # Get Instagram cookies using hex() to handle BLOB data
            cursor.execute("""
                SELECT host_key, name, value, path, expires_utc,
                       is_secure, is_httponly, creation_utc, last_access_utc,
                       hex(encrypted_value) as hex_encrypted_value
                FROM cookies
                WHERE host_key LIKE '%instagram.com%'
                ORDER BY name
            """)
            
            cookie_rows = cursor.fetchall()
            
            if not cookie_rows:
                print("❌ No Instagram cookies found.")
                return False
            
            print(f"\n🍪 Found {len(cookie_rows)} Instagram cookies:")
            print("=" * 80)
            
            decrypted_cookies = []
            
            for row in cookie_rows:
                # Convert hex back to bytes for encrypted value
                hex_encrypted = row['hex_encrypted_value']
                encrypted_value = bytes.fromhex(hex_encrypted) if hex_encrypted else None
                
                name = row['name']
                host = row['host_key']
                value = row['value']
                path = row['path']
                expires = row['expires_utc']
                secure = row['is_secure']
                httponly = row['is_httponly']
                created = row['creation_utc']
                last_access = row['last_access_utc']
                
                print(f"\n🍪 Cookie: {name}")
                print(f"   Host: {host}")
                print(f"   Path: {path}")
                
                # Try to decrypt the value
                if encrypted_value:
                    decrypted_value = self.decrypt_chrome_cookie_value(
                        encrypted_value, encryption_key, host, db_version
                    )
                    print(f"   Decrypted Value: {decrypted_value}")
                    
                    # Store for JSON export
                    decrypted_cookies.append({
                        'name': name,
                        'value': decrypted_value,
                        'domain': host,
                        'path': path,
                        'expires': expires,
                        'secure': bool(secure),
                        'httpOnly': bool(httponly),
                        'created': created,
                        'lastAccessed': last_access
                    })
                elif value:
                    print(f"   Plain Value: {value}")
                    decrypted_cookies.append({
                        'name': name,
                        'value': value,
                        'domain': host,
                        'path': path,
                        'expires': expires,
                        'secure': bool(secure),
                        'httpOnly': bool(httponly),
                        'created': created,
                        'lastAccessed': last_access
                    })
                else:
                    print(f"   Value: (empty)")
                
                print(f"   Secure: {bool(secure)}, HttpOnly: {bool(httponly)}")
                print(f"   Expires: {expires}")
                print("-" * 50)
            
            # Save decrypted cookies to JSON file
            with open(self.output_json, 'w') as f:
                json.dump(decrypted_cookies, f, indent=2)
            
            print(f"\n✅ Decrypted cookies saved to: {self.output_json}")
            return True
            
        except Exception as e:
            print(f"❌ Error extracting cookies: {e}")
            return False
        finally:
            conn.close()
    
    def cleanup(self):
        """Clean up temporary files"""
        if os.path.exists(self.temp_db_path):
            os.remove(self.temp_db_path)
            print(f"🧹 Cleaned up temporary file: {self.temp_db_path}")
    
    def run_full_extraction(self):
        """Run the complete extraction process"""
        print("🚀 Chrome Instagram Cookie Extractor")
        print("=" * 50)
        
        try:
            # Step 1: Copy Chrome cookies database
            if not self.copy_chrome_cookies():
                return False
            
            # Step 2: Explore database
            self.explore_database()
            
            # Step 3: Extract and decrypt Instagram cookies
            success = self.extract_and_decrypt_instagram_cookies()
            
            return success
            
        except KeyboardInterrupt:
            print("\n⚠️  Operation cancelled by user")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
        finally:
            # Always cleanup
            self.cleanup()


def main():
    parser = argparse.ArgumentParser(
        description="Extract and decrypt Instagram cookies from Chrome",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python chrome_instagram_cookie_extractor.py           # Run full extraction
  python chrome_instagram_cookie_extractor.py --explore # Just explore database
  python chrome_instagram_cookie_extractor.py --no-cleanup # Keep temp files
        """
    )
    
    parser.add_argument(
        '--explore-only', 
        action='store_true',
        help='Only explore the database, don\'t decrypt cookies'
    )
    
    parser.add_argument(
        '--no-cleanup',
        action='store_true', 
        help='Don\'t delete temporary database file'
    )
    
    args = parser.parse_args()
    
    extractor = ChromeCookieExtractor()
    
    try:
        if args.explore_only:
            # Just copy and explore
            if extractor.copy_chrome_cookies():
                extractor.explore_database()
        else:
            # Run full extraction
            success = extractor.run_full_extraction()
            if success:
                print("\n🎉 Instagram cookie extraction completed successfully!")
            else:
                print("\n❌ Instagram cookie extraction failed!")
                sys.exit(1)
    
    finally:
        if not args.no_cleanup:
            extractor.cleanup()


if __name__ == "__main__":
    # Check if required modules are available
    try:
        from cryptography.hazmat.primitives.ciphers import Cipher
    except ImportError:
        print("❌ Error: 'cryptography' module not found.")
        print("Install it with: pip install cryptography")
        sys.exit(1)
    
    main()
