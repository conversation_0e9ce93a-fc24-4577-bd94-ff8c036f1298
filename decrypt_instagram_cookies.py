#!/usr/bin/env python3
"""
Chrome Cookie Decryption Script for Instagram Cookies
Specifically designed for macOS Chrome cookie decryption
"""

import sqlite3
import os
import sys
import subprocess
import base64
import hashlib
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
import json


def get_chrome_encryption_key():
    """
    Get Chrome's encryption key from macOS keychain and derive the AES key
    """
    try:
        # Chrome stores its encryption key in the macOS keychain
        cmd = [
            'security', 'find-generic-password',
            '-w',  # Output only the password
            '-s', 'Chrome Safe Storage',  # Service name
            '-a', 'Chrome'  # Account name
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        passphrase = result.stdout.strip()

        if not passphrase:
            raise Exception("No encryption key found in keychain")

        # The passphrase from keychain is base64-encoded, use it directly
        passphrase_bytes = passphrase.encode('utf-8')

        # Derive the AES key using PBKDF2 with Chrome's parameters
        # Salt: "saltysalt", Iterations: 1003 (on macOS), Key length: 16 bytes
        salt = b'saltysalt'
        iterations = 1003

        # Use PBKDF2 with SHA-1 (Chrome's method)
        key = hashlib.pbkdf2_hmac('sha1', passphrase_bytes, salt, iterations, 16)

        return key

    except subprocess.CalledProcessError as e:
        print(f"Error accessing keychain: {e}")
        print("Make sure you have permission to access Chrome's keychain entry")
        return None
    except Exception as e:
        print(f"Error getting encryption key: {e}")
        return None


def get_database_version(cursor):
    """
    Get the Chrome database version to check if SHA256 hash is included
    """
    try:
        cursor.execute("SELECT value FROM meta WHERE key = 'version'")
        result = cursor.fetchone()
        return int(result[0]) if result else 0
    except:
        return 0


def decrypt_chrome_cookie_value(encrypted_value, key, host_key, db_version):
    """
    Decrypt Chrome cookie value using AES-128-CBC with proper Chrome algorithm
    """
    try:
        if not encrypted_value or len(encrypted_value) < 3:
            return ""

        # Check version tag
        version = encrypted_value[:3]

        if version != b'v10':
            print(f"Unsupported encryption version: {version}")
            return f"[Unsupported version: {version}]"

        # Strip version tag
        encrypted_payload = encrypted_value[3:]

        # Chrome uses AES-128-CBC with 16 space characters as IV
        iv = b' ' * 16  # 16 space characters (0x20)

        # Decrypt using AES-128-CBC
        cipher = Cipher(
            algorithms.AES(key),  # key should already be 16 bytes
            modes.CBC(iv),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(encrypted_payload) + decryptor.finalize()

        # Remove PKCS7 padding
        padding_length = decrypted[-1]
        if padding_length > 16 or padding_length == 0:
            raise ValueError(f"Invalid padding length: {padding_length}")

        # Verify padding
        padding_bytes = decrypted[-padding_length:]
        if not all(byte == padding_length for byte in padding_bytes):
            raise ValueError("Invalid padding bytes")

        # Remove padding
        decrypted_data = decrypted[:-padding_length]

        # Check if we need to remove SHA256 hash (database version >= 24)
        if db_version >= 24:
            # First 32 bytes are SHA256 hash of host_key, remove them
            if len(decrypted_data) >= 32:
                # Verify the hash matches (optional verification)
                expected_hash = hashlib.sha256(host_key.encode('utf-8')).digest()
                actual_hash = decrypted_data[:32]
                if expected_hash != actual_hash:
                    print(f"Warning: SHA256 hash mismatch for {host_key}")

                decrypted_data = decrypted_data[32:]
            else:
                print(f"Warning: Decrypted data too short to contain hash for {host_key}")

        # Decode to string
        return decrypted_data.decode('utf-8', errors='replace')

    except Exception as e:
        print(f"Decryption error for {host_key}: {e}")
        return f"[Decryption failed: {str(e)}]"


def decrypt_instagram_cookies():
    """
    Main function to decrypt Instagram cookies
    """
    # Path to Chrome cookies database
    cookies_db_path = "cookies_temp.db"
    
    if not os.path.exists(cookies_db_path):
        print(f"Error: {cookies_db_path} not found. Please run the cookie extraction script first.")
        return
    
    # Get Chrome's encryption key
    print("Getting Chrome encryption key from keychain...")
    encryption_key = get_chrome_encryption_key()
    
    if not encryption_key:
        print("Failed to get encryption key. Cannot decrypt cookies.")
        return
    
    print(f"Encryption key obtained (length: {len(encryption_key)} bytes)")
    
    # Connect to cookies database
    conn = sqlite3.connect(cookies_db_path)
    conn.row_factory = sqlite3.Row  # This allows us to access columns by name
    cursor = conn.cursor()

    # Get database version to check for SHA256 hash
    db_version = get_database_version(cursor)
    print(f"Chrome database version: {db_version}")
    if db_version >= 24:
        print("Note: Database includes SHA256 hash prefix (will be removed during decryption)")

    # Get Instagram cookies using hex() to handle BLOB data
    print("\nDecrypting Instagram cookies...")
    cursor.execute("""
        SELECT host_key, name, value, path, expires_utc,
               is_secure, is_httponly, creation_utc, last_access_utc,
               hex(encrypted_value) as hex_encrypted_value
        FROM cookies
        WHERE host_key LIKE '%instagram.com%'
        ORDER BY name
    """)

    cookie_rows = cursor.fetchall()
    instagram_cookies = []

    # Process each cookie and convert hex back to bytes
    for row in cookie_rows:
        hex_encrypted = row['hex_encrypted_value']
        encrypted_value = bytes.fromhex(hex_encrypted) if hex_encrypted else None

        # Create cookie data structure
        cookie_data = {
            'host_key': row['host_key'],
            'name': row['name'],
            'value': row['value'],
            'path': row['path'],
            'expires_utc': row['expires_utc'],
            'is_secure': row['is_secure'],
            'is_httponly': row['is_httponly'],
            'creation_utc': row['creation_utc'],
            'last_access_utc': row['last_access_utc'],
            'encrypted_value': encrypted_value
        }
        instagram_cookies.append(cookie_data)
    
    if not instagram_cookies:
        print("No Instagram cookies found.")
        conn.close()
        return
    
    print(f"\nFound {len(instagram_cookies)} Instagram cookies:")
    print("=" * 80)
    
    decrypted_cookies = []
    
    for cookie in instagram_cookies:
        name = cookie['name']
        host = cookie['host_key']
        value = cookie['value']
        path = cookie['path']
        expires = cookie['expires_utc']
        secure = cookie['is_secure']
        httponly = cookie['is_httponly']
        created = cookie['creation_utc']
        last_access = cookie['last_access_utc']
        encrypted_value = cookie['encrypted_value']

        print(f"\n🍪 Cookie: {name}")
        print(f"   Host: {host}")
        print(f"   Path: {path}")

        # Try to decrypt the value
        if encrypted_value:
            decrypted_value = decrypt_chrome_cookie_value(encrypted_value, encryption_key, host, db_version)
            print(f"   Decrypted Value: {decrypted_value}")

            # Store for JSON export
            decrypted_cookies.append({
                'name': name,
                'value': decrypted_value,
                'domain': host,
                'path': path,
                'expires': expires,
                'secure': bool(secure),
                'httpOnly': bool(httponly),
                'created': created,
                'lastAccessed': last_access
            })
        elif value:
            print(f"   Plain Value: {value}")
            decrypted_cookies.append({
                'name': name,
                'value': value,
                'domain': host,
                'path': path,
                'expires': expires,
                'secure': bool(secure),
                'httpOnly': bool(httponly),
                'created': created,
                'lastAccessed': last_access
            })
        else:
            print(f"   Value: (empty)")

        print(f"   Secure: {bool(secure)}, HttpOnly: {bool(httponly)}")
        print(f"   Expires: {expires}")
        print("-" * 50)
    
    # Save decrypted cookies to JSON file
    output_file = "instagram_cookies_decrypted.json"
    with open(output_file, 'w') as f:
        json.dump(decrypted_cookies, f, indent=2)
    
    print(f"\n✅ Decrypted cookies saved to: {output_file}")
    
    conn.close()


if __name__ == "__main__":
    print("🔓 Chrome Instagram Cookie Decryption Tool")
    print("=" * 50)
    
    # Check if required modules are available
    try:
        from cryptography.hazmat.primitives.ciphers import Cipher
    except ImportError:
        print("Error: 'cryptography' module not found.")
        print("Install it with: pip install cryptography")
        sys.exit(1)
    
    decrypt_instagram_cookies()
