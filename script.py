import sqlite3
import os
import shutil
import requests


def get_cookies():
    # Correct path for macOS Chrome cookies
    cookie_db = os.path.expanduser("~/Library/Application Support/Google/Chrome/Default/Cookies")
    temp_cookie_db = "cookies_temp.db"

    shutil.copyfile(cookie_db, temp_cookie_db)

    conn = sqlite3.connect(temp_cookie_db)
    cursor = conn.cursor()

    cursor.execute("SELECT host_key, name, value FROM cookies")

    cookies = cursor.fetchall()
    conn.close()


get_cookies()  # Call the correct function name
