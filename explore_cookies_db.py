import sqlite3
import os
import sys


def explore_cookies_db():
    temp_cookie_db = "cookies_temp.db"

    # Check if the database file exists
    if not os.path.exists(temp_cookie_db):
        print(f"Error: {temp_cookie_db} does not exist. Run the script to copy Chrome cookies first.")
        return

    conn = sqlite3.connect(temp_cookie_db)
    cursor = conn.cursor()

    # Check if the cookies table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cookies'")
    if not cursor.fetchone():
        print("Error: 'cookies' table does not exist in the database.")
        conn.close()
        return

    # Get table schema
    cursor.execute("SELECT sql FROM sqlite_schema WHERE type='table' AND name='cookies'")
    schema = cursor.fetchone()
    print("Cookies table schema:")
    print(schema[0] if schema else "Schema not found")

    # Get column names
    cursor.execute("PRAGMA table_info(cookies)")
    columns = cursor.fetchall()
    print("\nColumns in cookies table:")
    for col in columns:
        print(f"- {col[1]}: {col[2]}")

    # Count cookies
    cursor.execute("SELECT COUNT(*) FROM cookies")
    count = cursor.fetchone()[0]
    print(f"\nTotal cookies in database: {count}")

    if count == 0:
        print("No cookies found in the database.")
        conn.close()
        return

    # List all cookies with encrypted values
    print("\nCookies in database (note: values may be encrypted):")
    cursor.execute("SELECT host_key, name, value, path, expires_utc, is_secure, is_httponly FROM cookies LIMIT 10")
    all_cookies = cursor.fetchall()

    if all_cookies:
        for i, cookie in enumerate(all_cookies, 1):
            host, name, value, path, expires, secure, httponly = cookie
            print(f"{i}. Host: {host}, Name: {name}, Path: {path}")
            print(f"   Value: {value[:50]}{'...' if len(value) > 50 else ''}")
            print(f"   Encrypted: {'Yes' if value.startswith(b'v10') or value.startswith('v10') else 'No'}")
            print(f"   Secure: {bool(secure)}, HttpOnly: {bool(httponly)}")
            print(f"   Expires: {expires}")
            print("-" * 50)

    # Check if values are encrypted
    cursor.execute("SELECT value FROM cookies LIMIT 1")
    sample = cursor.fetchone()
    if sample and sample[0]:
        if isinstance(sample[0], bytes) and sample[0].startswith(b'v10'):
            print("\nNote: Cookie values appear to be encrypted with Chrome's encryption.")
            print("To decrypt, you would need the Chrome encryption key from your system keychain.")
        elif isinstance(sample[0], str) and sample[0].startswith('v10'):
            print("\nNote: Cookie values appear to be encrypted with Chrome's encryption.")
            print("To decrypt, you would need the Chrome encryption key from your system keychain.")

    conn.close()


explore_cookies_db()