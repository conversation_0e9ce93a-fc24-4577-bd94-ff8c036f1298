import sqlite3
import os
import sys


def explore_cookies_db():
    temp_cookie_db = "cookies_temp.db"

    # Check if the database file exists
    if not os.path.exists(temp_cookie_db):
        print(f"Error: {temp_cookie_db} does not exist. Run the script to copy Chrome cookies first.")
        return

    conn = sqlite3.connect(temp_cookie_db)
    cursor = conn.cursor()

    # Check if the cookies table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cookies'")
    if not cursor.fetchone():
        print("Error: 'cookies' table does not exist in the database.")
        conn.close()
        return

    # Get table schema
    cursor.execute("SELECT sql FROM sqlite_schema WHERE type='table' AND name='cookies'")
    schema = cursor.fetchone()
    print("Cookies table schema:")
    print(schema[0] if schema else "Schema not found")

    # Get column names
    cursor.execute("PRAGMA table_info(cookies)")
    columns = cursor.fetchall()
    print("\nColumns in cookies table:")
    for col in columns:
        print(f"- {col[1]}: {col[2]}")

    # Count total cookies
    cursor.execute("SELECT COUNT(*) FROM cookies")
    total_count = cursor.fetchone()[0]
    print(f"\nTotal cookies in database: {total_count}")

    # Count Instagram cookies
    cursor.execute("SELECT COUNT(*) FROM cookies WHERE host_key LIKE '%instagram.com%'")
    instagram_count = cursor.fetchone()[0]
    print(f"Instagram cookies in database: {instagram_count}")

    if total_count == 0:
        print("No cookies found in the database.")
        conn.close()
        return

    # List Instagram cookies only
    print("\nInstagram cookies in database (note: values may be encrypted):")
    # First, let's check what's in the value column vs encrypted_value column
    cursor.execute("SELECT host_key, name, value, length(encrypted_value) as enc_length, path, expires_utc, is_secure, is_httponly FROM cookies WHERE host_key LIKE '%instagram.com%'")
    instagram_cookies = cursor.fetchall()

    if instagram_cookies:
        for i, cookie in enumerate(instagram_cookies, 1):
            host, name, value, enc_length, path, expires, secure, httponly = cookie
            print(f"{i}. Host: {host}, Name: {name}, Path: {path}")

            # Check both value and encrypted_value columns
            has_plain_value = value and value.strip()
            has_encrypted_value = enc_length and enc_length > 0

            if has_plain_value:
                # Handle plain text value
                if isinstance(value, bytes):
                    value_str = value.decode('utf-8', errors='replace')[:50]
                else:
                    value_str = str(value)[:50]
                print(f"   Plain Value: {value_str}{'...' if len(str(value)) > 50 else ''}")

                # Check if it looks encrypted
                is_encrypted = False
                if isinstance(value, bytes):
                    is_encrypted = value.startswith(b'v10')
                elif isinstance(value, str):
                    is_encrypted = value.startswith('v10')
                print(f"   Value Encrypted: {'Yes' if is_encrypted else 'No'}")
            else:
                print(f"   Plain Value: (empty)")

            if has_encrypted_value:
                # Show info about encrypted value
                print(f"   Encrypted Value: Present ({enc_length} bytes)")
                print(f"   Chrome Encryption: Likely (stored in encrypted_value BLOB)")
            else:
                print(f"   Encrypted Value: (empty)")

            print(f"   Secure: {bool(secure)}, HttpOnly: {bool(httponly)}")
            print(f"   Expires: {expires}")
            print("-" * 50)
    else:
        print("No Instagram cookies found in the database.")

    # Check if values are encrypted
    cursor.execute("SELECT value FROM cookies LIMIT 1")
    sample = cursor.fetchone()
    if sample and sample[0]:
        if isinstance(sample[0], bytes) and sample[0].startswith(b'v10'):
            print("\nNote: Cookie values appear to be encrypted with Chrome's encryption.")
            print("To decrypt, you would need the Chrome encryption key from your system keychain.")
        elif isinstance(sample[0], str) and sample[0].startswith('v10'):
            print("\nNote: Cookie values appear to be encrypted with Chrome's encryption.")
            print("To decrypt, you would need the Chrome encryption key from your system keychain.")

    conn.close()


explore_cookies_db()